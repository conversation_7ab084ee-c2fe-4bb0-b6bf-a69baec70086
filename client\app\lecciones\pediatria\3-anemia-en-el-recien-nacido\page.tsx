"use client";

import { useEffect, useState } from "react";

// Define the structure of the data you expect from the API
interface Videoclase {
  id: number;
  title: string;
  especialidad: string;
  sistema: string;
  tema: string;
  url: string | null;
  description: string | null;
}

export default function AnemiaRecienNacidoPage() {
  const [videoclases, setVideoclases] = useState<Videoclase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchVideoclases() {
      try {
        // You can change the query parameters to filter by other criteria
        // Example: /api/contenido/videoclases?especialidad=Pediatría&sistema=Hematología&tema=Anemia
        const response = await fetch(
          "/api/contenido/videoclases?especialidad=Pediatría"
        );
        if (!response.ok) {
          throw new Error("Failed to fetch data");
        }
        const data: Videoclase[] = await response.json();
        setVideoclases(data);
      } catch (err) {
        if (err instanceof Error) {
            setError(err.message);
        } else {
            setError("An unknown error occurred");
        }
      } finally {
        setLoading(false);
      }
    }

    fetchVideoclases();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">
        Videoclases de Pediatría - Anemia en el Recién Nacido
      </h1>
      <p className="mb-4">
        This is an example of how to fetch data from the backend.
        This component fetches videoclases for the  especialidad.
        You can adapt this code to fetch other content types like casos clínicos, cuestionarios, etc., and apply more specific filters.
      </p>
      
      {videoclases.length > 0 ? (
        <ul>
          {videoclases.map((video) => (
            <li key={video.id} className="mb-2 p-2 border rounded">
              <h2 className="font-semibold">{video.title}</h2>
              <p>Tema: {video.tema}</p>
              {video.description && <p>{video.description}</p>}
            </li>
          ))}
        </ul>
      ) : (
        <p>No videoclases found for this topic.</p>
      )}
    </div>
  );
}
