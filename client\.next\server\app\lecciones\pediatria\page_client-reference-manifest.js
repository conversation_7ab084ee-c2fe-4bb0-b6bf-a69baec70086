globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/lecciones/pediatria/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sign-in/[[...sign-in]]/page.tsx":{"*":{"id":"(ssr)/./app/sign-in/[[...sign-in]]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./providers/top-provider.tsx":{"*":{"id":"(ssr)/./providers/top-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/planeador/page.tsx":{"*":{"id":"(ssr)/./app/planeador/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/upload/page.tsx":{"*":{"id":"(ssr)/./app/upload/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/lecciones/medicina-interna/page.tsx":{"*":{"id":"(ssr)/./app/lecciones/medicina-interna/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/lecciones/pediatria/page.tsx":{"*":{"id":"(ssr)/./app/lecciones/pediatria/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\sign-in\\[[...sign-in]]\\page.tsx":{"id":"(app-pages-browser)/./app/sign-in/[[...sign-in]]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\providers\\top-provider.tsx":{"id":"(app-pages-browser)/./providers/top-provider.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\planeador\\page.tsx":{"id":"(app-pages-browser)/./app/planeador/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\upload\\page.tsx":{"id":"(app-pages-browser)/./app/upload/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\lecciones\\medicina-interna\\page.tsx":{"id":"(app-pages-browser)/./app/lecciones/medicina-interna/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\lecciones\\pediatria\\page.tsx":{"id":"(app-pages-browser)/./app/lecciones/pediatria/page.tsx","name":"*","chunks":["app/lecciones/pediatria/page","static/chunks/app/lecciones/pediatria/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\":[],"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\page":[],"C:\\Users\\<USER>\\OneDrive\\Documentos\\0. TuResiBo\\client\\app\\lecciones\\pediatria\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/sign-in/[[...sign-in]]/page.tsx":{"*":{"id":"(rsc)/./app/sign-in/[[...sign-in]]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./providers/top-provider.tsx":{"*":{"id":"(rsc)/./providers/top-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/planeador/page.tsx":{"*":{"id":"(rsc)/./app/planeador/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/upload/page.tsx":{"*":{"id":"(rsc)/./app/upload/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/lecciones/medicina-interna/page.tsx":{"*":{"id":"(rsc)/./app/lecciones/medicina-interna/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/lecciones/pediatria/page.tsx":{"*":{"id":"(rsc)/./app/lecciones/pediatria/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}