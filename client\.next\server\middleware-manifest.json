{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vpuyGpnRGjkFou8C30vySVISi+U2geFoS/CwcDPvqwM=", "__NEXT_PREVIEW_MODE_ID": "e1dc7be5d9b4ef24da0ecd02b5ea6823", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d421b6ababbe00ad5f272749d501dc775f7e11070ac1638f4de0da1a3994a09e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8ca3df8ec65bf685a8ac547bd4ba0a18d51edb40f6fe305b440fb681135bf1ca"}}}, "functions": {}, "sortedMiddleware": ["/"]}