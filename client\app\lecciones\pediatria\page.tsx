"use client";

import { useEffect, useState } from "react";
import Link from "next/link";

// Define interfaces for the content types
interface ContentItem {
  id: number;
  title: string;
  tema: string;
  sistema: string;
}

const especialidad = "Pediatria";

async function fetchContent<T>(contentType: string): Promise<T[]> {
  const response = await fetch(
    `/api/contenido/${contentType}?especialidad=${especialidad}`
  );
  if (!response.ok) {
    throw new Error(`Failed to fetch ${contentType}`);
  }
  return response.json();
}

function ContentSection<T extends ContentItem>({
  title,
  data,
  basePath,
}: {
  title: string;
  data: T[] | null;
  basePath: string;
}) {
  if (!data || data.length === 0) {
    return null;
  }

  return (
    <section className="mb-8">
      <h2 className="text-2xl font-semibold mb-4">{title}</h2>
      <ul className="space-y-2">
        {data.map((item) => (
          <li key={item.id} className="p-2 border rounded hover:bg-gray-100">
            <Link href={`/lecciones/pediatria/${basePath}/${item.id}`}>
              <span className="font-medium">{item.title}</span> -{" "}
              <span className="text-sm text-gray-600">
                {item.sistema} / {item.tema}
              </span>
            </Link>
          </li>
        ))}
      </ul>
    </section>
  );
}

export default function PediatriaPage() {
  const [content, setContent] = useState<{ [key: string]: ContentItem[] | null }>({
    videoclases: null,
    videos_cortos: null,
    notas_clinicas: null,
    casos_clinicos: null,
    cuestionarios: null,
    flashcards: null,
    repaso: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadContent() {
      try {
        const contentTypes = [
          "videoclases",
          "videos-cortos",
          "notas-clinicas",
          "casos-clinicos",
          "cuestionarios",
          "flashcards",
          "repaso",
        ];
        
        const promises = contentTypes.map(type => fetchContent<ContentItem>(type.replace(/-/g, '_')));
        const results = await Promise.all(promises);

        const newContent: { [key: string]: ContentItem[] | null } = {};
        contentTypes.forEach((type, index) => {
            newContent[type.replace(/-/g, '_')] = results[index];
        });

        setContent(newContent);

      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred");
        }
      } finally {
        setLoading(false);
      }
    }

    loadContent();
  }, []);

  if (loading) {
    return <div className="container mx-auto p-4">Loading content...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-4">Error: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Lecciones de Pediatría</h1>

      <ContentSection
        title="Videoclases"
        data={content.videoclases}
        basePath="videoclases"
      />
      <ContentSection
        title="Videos Cortos"
        data={content.videos_cortos}
        basePath="videos-cortos"
      />
      <ContentSection
        title="Notas Clínicas"
        data={content.notas_clinicas}
        basePath="notas-clinicas"
      />
      <ContentSection
        title="Casos Clínicos"
        data={content.casos_clinicos}
        basePath="casos-clinicos"
      />
      <ContentSection
        title="Cuestionarios"
        data={content.cuestionarios}
        basePath="cuestionarios"
      />
      <ContentSection
        title="Flashcards"
        data={content.flashcards}
        basePath="flashcards"
      />
      <ContentSection 
        title="Repaso" 
        data={content.repaso} 
        basePath="repaso" 
      />
    </div>
  );
}